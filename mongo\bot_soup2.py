import traceback
from urllib.parse import urlparse
import aiofiles
import requests
import os
from bs4 import BeautifulSoup
import json  # Import json module to parse JSON data
from datetime import datetime
import re
import time
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from time import sleep
import pytz
import random
import gc
import aiohttp
import asyncio

tz = pytz.timezone("Asia/Hong_Kong")


class SoupBot2:
    def __init__(
        self,
        exam_code="",
        examdumps_folder="examdumps",
        qna_folder="qna",
        qna_images_folder="images",
        json_folder="json",
        json_file="data.json",
        domain="https://www.examtopics.com",
        vendor_folder="",
        max_concurrency=100,
        image_download_path="C:\\Users\\<USER>\\dev\\rn_node\\src\\public\\images"
    ):
        self.semaphore = asyncio.Semaphore(max_concurrency)
        self.processed_count = 0  # Initialize a counter
        self.session = None
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
        }
        # Define the directory to save the images
        self.examdumps_folder = examdumps_folder
        self.vendor_folder = vendor_folder
        self.exam_code = exam_code
        self.qna_folder = qna_folder
        self.qna_images_folder = qna_images_folder
        self.json_folder = json_folder

        """ self.qna_dir = os.path.join(
            examdumps_folder, vendor_folder, exam_code, self.qna_folder
        ) """
        self.image_dir = os.path.join(image_download_path, self.qna_images_folder)
        self.image_src_folder = qna_images_folder

        self.json_exam_dir = os.path.join(
            examdumps_folder, self.json_folder, exam_code)
        self.data_json_path = os.path.join(self.json_exam_dir, json_file)

        self.domain = domain

        self.file_list = []

        os.makedirs(
            self.image_dir, exist_ok=True
        )  # Create the directory if it doesn't exist

        os.makedirs(
            self.json_exam_dir, exist_ok=True
        )  # Create the directory if it doesn't exist

    async def initialize_session(self):
        self.session = aiohttp.ClientSession()

    # Function to get a random User-Agent
    def get_random_user_agent(self):
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
            "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Mobile Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 9; SM-J530F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
            "Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Windows NT 6.3; Trident/7.0; AS; rv:11.0) like Gecko",
            "Mozilla/5.0 (Linux; Android 11; Galaxy S21) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Mobile Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Linux; Android 8.0.0; Nexus 5X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Mobile Safari/537.36",
        ]
        return random.choice(user_agents)

    def setExamCode(self, exam_code):
        self.exam_code = exam_code

    def setJSONFile(self, json_file):
        self.json_file = json_file

    def get_full_url(self, url, domain=None):
        """
        Convert a relative URL to a full URL or return the full URL as is.

        Parameters:
        - url (str): The input URL which can be absolute or relative.
        - domain (str): The domain to use if the URL is relative.

        Returns:
        - str: The valid full URL or an error message if the domain is invalid.
        """
        if domain is None:
            domain = self.domain

        # Check if the input URL is already a full URL
        if url.startswith("http://") or url.startswith("https://"):
            return url

        # Check if the domain is provided
        if not domain:
            return "Invalid domain"

        # Ensure the domain starts with the scheme (http or https)
        if not domain.startswith("http://") and not domain.startswith("https://"):
            domain = f"https://{domain}"

        # Ensure the url starts with a forward slash for relative URLs
        if not url.startswith("/"):
            url = "/" + url

        # Combine them into a valid full URL
        full_url = f"{domain}{url}"
        return full_url

    def formatJSONName(self, date_only, html_title, format="json"):
        """
        Extract topic and question numbers from the URL and format the designated filename.
        """

        # Define possible date formats
        date_only = date_only.replace("Sept.", "Sep.")

        date_formats = [
            # Abbreviated month with a period (e.g., "Sept. 1, 2022")
            "%b. %d, %Y",
            # Abbreviated month without a period (e.g., "Sept 1, 2022")
            "%b %d, %Y",
            "%B %d, %Y",  # Full month name (e.g., "July 5, 2024")
        ]

        # Attempt to parse the date using the defined formats
        formatted_date = None
        for fmt in date_formats:
            try:
                date_obj = datetime.strptime(date_only, fmt)
                formatted_date = date_obj.strftime(
                    "%Y%m%d")  # Format as YYYYMMDD
                break  # Exit the loop on successful parsing
            except ValueError:
                continue  # Try the next format

        # If no formats match, raise an error
        if formatted_date is None:
            raise ValueError(
                f"Date format for '{date_only}' is not recognized.")

        # Assume the page is already loaded; get the title
        """ html_title = self.driver.title """
        topic_number = self.extract_topic_number(html_title)
        question_number = self.extract_question_number(html_title)

        # Format topic and question numbers
        topic_number_formatted = f"{int(topic_number):02d}"
        question_number_formatted = f"{int(question_number):03d}"

        # Remove html title
        self.driver.execute_script("$('title').remove()")

        # Return the formatted filename
        return f"{formatted_date}.{topic_number_formatted}.{question_number_formatted}.{format}"

    def extract_topic_number(self, html_title):
        """Extract topic number from the HTML title."""
        match = re.search(r"topic (\\d+)", html_title)
        return match.group(1) if match else None

    def extract_question_number(self, html_title):
        """Extract question number from the HTML title."""
        match = re.search(r"question (\\d+)", html_title)
        return match.group(1) if match else None

    def extract_id_from_url(self, url):
        """Extract ID from the URL to ensure uniqueness."""
        pattern = r"/view/(\d+)-"
        match = re.search(pattern, url)
        return f"-{match.group(1)}" if match else ""

    def get_total_pages(self, url):
        """
        Get the total number of pages on the list page and save the domain.
        """

        """ # Parse the URL
        parsed_url = urlparse(url)

        # Get the domain part
        self.domain = parsed_url.netloc """

        # Send a GET request to the URL
        response = self.fetch_url_old(self.get_full_url(url))

        if response is None:
            return None

        # Parse the HTML content
        soup = BeautifulSoup(response.content, "lxml")

        # Get all the options in the dropdown
        options = soup.select(
            ".discussion-list-page-select option"
        )  # Use select to get elements

        # Check if options were found
        if not options:
            print("No options found in the dropdown.")
            return 1  # Return None if no options are found

        # Get the value of the last option
        try:
            total_pages = int(
                options[-1]["value"]
            )  # Use ['value'] instead of get_attribute
        except (IndexError, ValueError) as e:
            print(f"Error retrieving total pages: {e}")
            return None  # Return None if there's an issue with the value

        return total_pages

    async def get_discussion_links_per_page(self, url):
        """
        Find all the discussion links related to the given code.
        """

        # Send a GET request to the URL
        html_content = await self.fetch_url(self.get_full_url(url))
        if not html_content:
            return None  # Return None if the request failed

        # Parse the HTML content
        soup = BeautifulSoup(html_content, "lxml")

        # Find all the discussion links
        discussion_links = soup.select(".discussion-link")

        # Filter the links that are related to the given code
        filtered_links = []
        for link in discussion_links:
            href = link.get("href")  # Use .get() to access the href attribute
            if (
                href and
                (self.exam_code.lower() in href.lower())
            ):  # Ensure href is not None
                """ or
                 (self.exam_code.lower() ==
                  "aws-certified-machine-learning-engineer-associate-mla-c01" and
                  "aws-certified-machine-learning-engineer-associate-mla" in href.lower() ) # this exception is for the AWS MLS-C01 exam
                 or
                 (self.exam_code.lower() ==
                  "fcp-fgt-ad-7-4" and
                  "fcp_fgt_ad-74" in href.lower() ) # this exception is for the FCP - FortiGate 7.4 Administrator exam
                 or
                 (self.exam_code.lower() ==
                  "ctfl-v4-0" and
                  "ctfl-v40" in href.lower() ) # this exception is for the Certified Tester Foundation Level (CTFL) v4.0 exam
                 or
                 (self.exam_code.lower() ==
                  "fcp-fmg-ad-7-4" and
                  "fcp_fmg_ad-74" in href.lower() ) # this exception is for the FCP - FortiManager 7.4 Administrator exam
                ) """
                filtered_links.append(href)

        # print(filtered_links)
        """ for filtered_link in filtered_links:
            yield filtered_link """
        return filtered_links

    def get_discussion_links_per_page_old(self, url):
        """
        Find all the discussion links related to the given code.
        """

        # Send a GET request to the URL
        response = self.fetch_url_old(self.get_full_url(url))

        # Parse the HTML content
        soup = BeautifulSoup(response.content, "lxml")

        # Find all the discussion links
        discussion_links = soup.select(".discussion-link")

        # Filter the links that are related to the given code
        filtered_links = []
        for link in discussion_links:
            href = link.get("href")  # Use .get() to access the href attribute
            if (
                href and self.exam_code.lower() in href.lower()
            ):  # Ensure href is not None
                filtered_links.append(href)

        # print(filtered_links)
        for filtered_link in filtered_links:
            yield filtered_link
        # return filtered_links

    def parse_comments(self, soup):
        comments = []
        for comment in soup.select(".discussion-container > .comment-container"):
            comments.append(self.parse_comment(comment))
        return comments

    def parse_comment(self, comment):
        user = comment.find("h5", class_="comment-username").text.strip()
        text = comment.find("div", class_="comment-content").text.strip()
        text = text.replace("\n", "<br>")  # Replace newlines with <br> tags
        upvotes = int(comment.find("span", class_="upvote-count").text.strip())

        # Handling the timestamp
        timestamp_str = comment.find("span", class_="comment-date")["title"]
        timestamp = (
            datetime.strptime(
                timestamp_str, "%a %d %b %Y %H:%M").isoformat() + "Z"
        )

        # Check for tags in the badges
        tags = [
            badge.text.strip() for badge in comment.find_all("span", class_="badge")
        ]

        # Check for nested comments
        nested_comments = []
        replies = comment.find("div", class_="comment-replies")
        if replies:
            for nested_comment in replies.find_all("div", class_="comment-container"):
                nested_comments.append(self.parse_comment(nested_comment))

        return {
            "user": user,
            "text": text,
            "timestamp": timestamp,
            "upvotes": upvotes,
            "tags": tags,  # Now includes all badges
            "nested_comments": nested_comments,
        }

    def downloadImage(self, img, max_retries=5, delay=5):
        if "src" in img.attrs:
            img_url = img["src"]  # Get the image URL
            # Split the URL to get the path after the domain
            # Get the part after the domain
            path_parts = img_url.split("/", 3)[-1:]

            img_path = os.path.join(
                self.image_dir, *path_parts
            )  # Create the local path
            img_src = os.path.join(self.image_src_folder,
                                   *path_parts)  # Create the URL

            os.makedirs(
                os.path.dirname(img_path), exist_ok=True
            )  # Create the directory if it doesn't exist

            # Download the image with retry logic
            for attempt in range(max_retries):
                try:
                    img_response = self.fetch_url_old(
                        self.get_full_url(img_url))
                    img_response.raise_for_status()  # Raise an error for bad responses

                    with open(img_path, "wb") as f:
                        f.write(img_response.content)  # Save the image
                    print(f"Image saved locally as {img_path}")
                    img["src"] = img_src.replace("\\", "/")
                    return  # Exit after successfully downloading the image
                except requests.HTTPError as e:
                    print(
                        f"Failed to download {img_url}. Status code: {e.response.status_code}"
                    )
                    break  # Exit on HTTP error
                except requests.RequestException as e:
                    print(
                        f"An error occurred while downloading {img_url}: {e}")
                    if attempt < max_retries - 1:
                        print(f"Retrying in {delay} seconds...")
                        time.sleep(delay)  # Wait before retrying

        else:
            print("No 'src' attribute found in the image tag.")

    async def downloadImageNew(self, img, max_retries=5, delay=5):
        """
        Download an image asynchronously with retry logic.
        """
        try:
            if "src" in img.attrs:
                img_url = img["src"]  # Get the image URL
                # Split the URL to get the path after the domain
                # Get the part after the domain
                path_parts = img_url.split("/", 3)[-1:]

                img_path = os.path.join(
                self.image_dir, *path_parts
            )  # Create the local path
            img_src = os.path.join("", *path_parts)  # Use empty string as prefix

            os.makedirs(
                os.path.dirname(img_path), exist_ok=True
            )  # Create the directory if it doesn't exist

            # Download the image with retry logic
            for attempt in range(max_retries):
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            self.get_full_url(img_url)
                        ) as img_response:
                            img_response.raise_for_status()  # Raise an error for bad responses

                            # Save the image asynchronously
                            async with aiofiles.open(img_path, "wb") as f:
                                await f.write(
                                    await img_response.read()
                                )  # Write the image content
                            print(f"Image saved locally as {img_path}")
                            img["src"] = img_src.replace("\\", "/")
                            return  # Exit after successfully downloading the image
                except Exception as e:  # More generic exception handling
                    print(f"Failed to download {img_url}: {e}")
                    if attempt < max_retries - 1:
                        print(f"Retrying in {delay} seconds...")
                        await asyncio.sleep(delay)  # Non-blocking sleep
                    else:
                        print(
                            f"Max retries ({max_retries}) reached for {img_url}.")
                        break  # Exit on max retries
            else:
                print("No 'src' attribute found in the image tag.")

        except Exception as e:
            # Catch any exception and handle it
            error_type = type(e).__name__  # Get the exception type
            error_message = str(e)  # Get the exception message
            traceback_str = traceback.format_exc()
            print(f"An error occurred: {error_message}")
            print(f"{error_type}")
            print(f"{traceback_str}")

    def clean_html(self, html_content):
        """
        Clean HTML content by removing unwanted tags and fixing formatting issues.

        This function:
        1. Removes <html> and <body> tags
        2. Removes <p> tags that appear immediately before or after <img> tags
        3. Removes <br> tags that appear immediately before or after <img> tags
        4. Removes outermost <p> tags at the beginning and end of content
        """
        if not html_content:
            return html_content

        # Parse the HTML content
        soup = BeautifulSoup(html_content, "lxml")

        # Remove html and body tags but keep their contents
        if soup.html:
            soup.html.unwrap()
        if soup.body:
            soup.body.unwrap()

        # Find all img tags
        img_tags = soup.find_all('img')

        for img in img_tags:
            # Check for and remove <p> tags immediately before or after <img>
            if img.parent and img.parent.name == 'p' and len(img.parent.contents) == 1:
                # If the <p> tag only contains this image, replace it with just the image
                img.parent.replace_with(img)

            # Check for and remove <br> tags immediately before <img>
            prev_sibling = img.previous_sibling
            if prev_sibling and prev_sibling.name == 'br':
                prev_sibling.decompose()

            # Check for and remove <br> tags immediately after <img>
            next_sibling = img.next_sibling
            if next_sibling and next_sibling.name == 'br':
                next_sibling.decompose()

        # Additional direct string manipulation to catch any remaining p tags
        result = str(soup)

        # If the content still starts with <p> and ends with </p>, remove them
        result = result.strip()

        # Use regex to handle cases where there might be whitespace or other content
        # before/after the paragraph tags
        import re

        # Check for content that starts with <p> (possibly with whitespace before it)
        # and ends with </p> (possibly with whitespace after it)
        p_tag_pattern = re.compile(r'^\s*<p>(.*)</p>\s*$', re.DOTALL)
        match = p_tag_pattern.match(result)

        if match:
            # Extract the content inside the p tags
            inner_content = match.group(1)

            # Count the number of <p> and </p> tags in the inner content
            inner_p_open_count = inner_content.count('<p>')
            inner_p_close_count = inner_content.count('</p>')

            # If there are no nested p tags, we can safely remove the outer ones
            if inner_p_open_count == 0 and inner_p_close_count == 0:
                result = inner_content
                #print("Removed outer p tags using regex")
            """ else: """
                #print(f"Nested p tags detected: {inner_p_open_count} opening, {inner_p_close_count} closing - keeping structure intact")
        else:
            # Check for multiple p tags
            p_open_count = result.count('<p>')
            p_close_count = result.count('</p>')

            if p_open_count > 0 or p_close_count > 0:
                #print(f"P tags detected but not as simple wrapper: {p_open_count} opening, {p_close_count} closing")

                # Try to remove p tags at the very beginning and end if they exist
                if result.lstrip().startswith('<p>') and result.rstrip().endswith('</p>'):
                    # Find the position of the first <p> and last </p>
                    first_p_pos = result.find('<p>')
                    last_p_pos = result.rfind('</p>')

                    if last_p_pos > first_p_pos + 3:  # Make sure there's content between them
                        # Remove the first <p> and last </p>
                        result = result[:first_p_pos] + result[first_p_pos+3:last_p_pos] + result[last_p_pos+4:]
                        #print("Removed first and last p tags using position-based approach")

        # Debug: Print the final result
        #print(f"Final cleaned HTML (first 100 chars): {result[:100]}...")

        # Return the cleaned HTML
        return result
  
    def parseDate(self, date_time_text):
        # print(date_time_text)
        # Replace "Sept." with "Sep."
        date_time_text = date_time_text.replace("Sept.", "Sep.")

        # Regular expression to match the date part, allowing for trailing text
        date_pattern = r"(\b(?:Jan\.|Feb\.|Mar\.|Apr\.|May\.|Jun\.|Jul\.|Aug\.|Sep\.|Oct\.|Nov\.|Dec\.|January|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}, \d{4})"

        # Search for the date pattern in the input string
        match = re.search(date_pattern, date_time_text)

        if match:
            date_str = match.group(1)  # Extract the matched date string

            # Try parsing with both formats
            date_formats = ["%b. %d, %Y", "%B %d, %Y"]

            for date_format in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, date_format)
                    return parsed_date.strftime("%Y-%m-%d")
                except ValueError:
                    continue  # Try the next format if parsing fails

            print("Date parsing error: Unable to parse the date with known formats.")
            return None
        else:
            print("No valid date found.")
            return None

    async def process_multiple_pages(self, urls, process_func=None):
        """
        Processes multiple pages concurrently.
        """
        if process_func is None:
            process_func = self.processPage  # Default to self.processPage

        # Reset the processed count at the beginning of each call
        self.processed_count = 0

        async def semaphores_process(url):
            async with self.semaphore:
                try:
                    result = await process_func(url)
                    await update_processed_count()  # Update and print the count
                    return result
                except Exception as e:
                    print(f"Error processing URL {url}: {str(e)}")
                    import traceback
                    print(f"Traceback: {traceback.format_exc()}")
                    await update_processed_count()  # Still update count even on error
                    return None  # Return None on error

        async def update_processed_count():
            self.processed_count += 1
            print(f"{process_func.__name__}: {self.processed_count} of {len(urls)}")

        try:
            # Process URLs in smaller batches to avoid memory issues
            if len(urls) > 500:
                print(f"Processing {len(urls)} URLs in batches")
                batch_size = 500
                all_results = []

                for i in range(0, len(urls), batch_size):
                    batch = urls[i:i+batch_size]
                    print(f"Processing batch {i//batch_size + 1} of {(len(urls) + batch_size - 1) // batch_size}")

                    # Create tasks for this batch
                    batch_tasks = [semaphores_process(url) for url in batch]
                    # Run tasks concurrently
                    batch_results = await asyncio.gather(*batch_tasks, return_exceptions=False)
                    all_results.extend(batch_results)

                    # Force garbage collection between batches
                    import gc
                    gc.collect()
                    print(f"Completed batch {i//batch_size + 1}")

                return all_results
            else:
                # For smaller URL lists, process all at once
                tasks = [semaphores_process(url) for url in urls]
                results = await asyncio.gather(*tasks, return_exceptions=False)
                return results
        except Exception as e:
            print(f"Error in process_multiple_pages: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return [None] * len(urls)  # Return a list of None values matching the length of urls
        # Note: Session closing is now handled in the session_close method

    async def processPage(self, url):
        """
        Asynchronously processes a single page.
        """
        # Send a GET request to the URL
        html_content = await self.fetch_url(self.get_full_url(url))
        if not html_content:
            return None  # Return None if the request failed

        # Parse the HTML content
        # soup = BeautifulSoup(response.content, "html.parser")
        soup = BeautifulSoup(html_content, "lxml")

        # Extract the suggested answer using the correct class
        suggested_answer = soup.select_one(".correct-answer")
        suggested_answer_text = (
            suggested_answer.decode_contents().strip()
            if suggested_answer
            else "No correct answer found."
        )

        # Check if the suggested answer is an image or text
        if suggested_answer and suggested_answer.find("img"):
            # If it's an image, download it
            img_tags = suggested_answer.find_all("img")
            for img in img_tags:
                await self.downloadImageNew(img)
            suggested_answer_text = suggested_answer.decode_contents()

            # Clean up the HTML after image processing
            suggested_answer_text = self.clean_html(suggested_answer_text)

        # Remove unwanted elements
        for unwanted_class in [
            "hide-solution",
            "question-answer",
            "reveal-solution",
        ]:
            for element in soup.find_all(class_=unwanted_class):
                element.decompose()  # Removes the element from the tree

        # Find images within the card text
        # Select the specific parent
        card_text = soup.select_one("p.card-text")
        img_tags = (
            card_text.find_all("img") if card_text else []
        )  # Ensure card_text is not None

        # Download images
        for img in img_tags:
            await self.downloadImageNew(img)

        # Extract the question
        # Get the question element
        question = soup.select_one(".question-body")
        question_cleaned = (
            question.decode_contents() if question else "Question not found."
        )

        # Debug: Print the HTML content before cleaning
        #print(f"HTML content before cleaning (first 100 chars): {question_cleaned[:100]}...")

        # Clean up the HTML after image processing
        question_cleaned = self.clean_html(question_cleaned)

        # Debug: Print the HTML content after cleaning
        #print(f"HTML content after cleaning (first 100 chars): {question_cleaned[:100]}...")

        # Clean up whitespace
        question_cleaned = " ".join(
            question_cleaned.split()
        ).strip()

        # Parse the HTML
        soup2 = BeautifulSoup(question_cleaned, "lxml")

        # Get the text from the parsed HTML
        question_cleaned2 = soup2.get_text(separator=" ")

        # Use regex to extract the first sentence
        first_sentence_match = re.match(r"([^.!?]*[.!?])", question_cleaned2)

        # Get the first sentence if it exists
        first_sentence = first_sentence_match.group(
            0) if first_sentence_match else ""

        # Extract vote distribution from the JSON in the script tag
        vote_distribution = {}
        voted_answers_tally = soup.select_one(".voted-answers-tally script")

        if voted_answers_tally:
            try:
                # Load the JSON data from the script tag
                vote_data = json.loads(voted_answers_tally.string)
                for item in vote_data:
                    answer = item["voted_answers"]
                    count = item["vote_count"]
                    vote_distribution[answer] = count
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON data: {e}")
            except TypeError as e:
                print(f"Type error: {e}")

        # Convert comments to JSON format
        """ comments_json = {
            "comments": self.parse_comments(soup.select_one(".discussion-container"))
        } """

        # Locate the div containing the date-time using Beautiful Soup
        # formatted_date = timestamp = ""
        date_time_elements = soup.select(
            "div.discussion-meta-data.mt-3.pt-1.border-top i"
        )

        date_text = None
        if date_time_elements:  # We expect at least 2 <i> tags
            date_time_element = (
                date_time_elements[1]
                if len(date_time_elements) > 1
                else date_time_elements[0]
            )
            date_time_text = date_time_element.get_text(
                strip=True
            )  # e.g., "Nov. 25, 2023, 5:33 p.m."
            date_text = self.parseDate(date_time_text)

        """ title = soup.title.string if soup.title else
        match = re.search(r"topic (\\d+)", title)
        topic_number = match.group(1) if match else

        match = re.search(r"question (\\d+)", title)
        question_number = match.group(1) if match else  """

        # Define Hong Kong timezone
        record = {
            "date_added": date_text,
            # "topic": topic_number,
            # "question": question_number,
            # "url": url,
            "first_sentence": first_sentence.strip(),
            "exam": {
                "question": question_cleaned,
                "answer": suggested_answer_text,
            },
            # "comments": comments_json["comments"],
            "vote_distribution": vote_distribution,
        }
        # print(record["date_added"])

        # Extract the exam field to get question and choices
        exam_content = record["exam"]

        # Extract the question
        question_match = re.search(
            r'<p class="card-text">(.*?)</p>',
            exam_content["question"],
            re.DOTALL,
        )
        question = (
            question_match.group(1).replace("<br/>", "\n").strip()
            if question_match
            else ""
        )

        record["type"] = "mc"
        conditions = [
            ("HOTSPOT  - ", "HOTSPOT\n -\n\n", "fill"),
            (
                "HOTSPOT  - ",
                '<p class="card-text"> HOTSPOT<br/> -<br/><br/>',
                "fill",
            ),
            ("HOTSPOT - ", "HOTSPOT -\n", "fill"),
            ("HOTSPOT - ", '<p class="card-text"> HOTSPOT -<br/>', "fill"),
            ("DRAG DROP  - ", "DRAG DROP\n -\n\n", "drag"),
            ("DRAG DROP - ", "DRAG DROP -\n", "drag"),
            (
                "DRAG DROP - ",
                '<p class="card-text"> DRAG DROP -<br/><br/>',
                "drag",
            ),
            ("DRAG DROP - ", '<p class="card-text"> DRAG DROP -<br/>', "drag"),
        ]

        for prefix, question_format, question_type in conditions:
            if record["first_sentence"].startswith(prefix):
                record["first_sentence"] = record["first_sentence"].replace(
                    prefix, "", 1
                )
                question = question.replace(question_format, "", 1)
                record["type"] = question_type
                break  # Exit the loop after processing the first matching condition

        answer_string = exam_content["answer"]
        # Check if the answer_string is an HTML image tag
        if re.match(r"<img\s+[^>]*?>", answer_string):
            answer_list = answer_string  # Assign the HTML image string directly
        else:
            # Convert the answer string to a list of characters
            answer_list = (
                list(answer_string) if answer_string else []
            )  # Split string into list

        # Check if answer_list is a list and has more than 1 item
        if isinstance(answer_list, list) and len(answer_list) > 1:
            record["type"] = "mc2"

        # Create a new record
        filtered_record = {
            "date_added": date_text,
            "first_sentence": record["first_sentence"],
            "type": record["type"],
            "exam": {
                "question": question,
                "answer": answer_list,  # Preserve the original answer as a list
            },
            "vote_distribution": record["vote_distribution"],
        }

        # Extract the choices (including the correct answer)
        choices = re.findall(
            r'<li class="multi-choice-item(?: correct-hidden)?">.*?<span class="multi-choice-letter".*?>(.*?)</span> (.*?)</li>',
            exam_content["question"],
            re.DOTALL,
        )
        if choices:
            # Prepare choices and answer
            choices_list = []

            for choice in choices:
                # Remove the dot from the letter
                letter = choice[0].strip().rstrip(
                    ".")  # Remove any trailing dot
                text = choice[1].strip()

                # Process images in choice text
                if "<img" in text:
                    # Create a temporary BeautifulSoup object to find and process images
                    choice_soup = BeautifulSoup(text, "lxml")
                    img_tags = choice_soup.find_all("img")

                    # Download and replace image URLs
                    for img in img_tags:
                        await self.downloadImageNew(img)

                        # Clean up img tags and surrounding elements
                        # Remove p tags wrapping single images
                        if img.parent and img.parent.name == 'p' and len(img.parent.contents) == 1:
                            img.parent.replace_with(img)

                        # Remove br tags before/after images
                        prev_sibling = img.previous_sibling
                        if prev_sibling and prev_sibling.name == 'br':
                            prev_sibling.decompose()

                        next_sibling = img.next_sibling
                        if next_sibling and next_sibling.name == 'br':
                            next_sibling.decompose()

                    # Remove starting and ending p tags from choices
                    while choice_soup.find('p') and len(choice_soup.find('p').contents) == 1 and not choice_soup.find('p').find_next_sibling():
                        choice_soup.find('p').unwrap()

                    # Get the updated HTML with replaced image URLs
                    text = str(choice_soup)

                    # Debug: Print the choice HTML content before cleaning
                    #print(f"Choice HTML before cleaning (first 50 chars): {text[:50]}...")

                    # Clean up the HTML after image processing
                    text = self.clean_html(text)

                    # Debug: Print the choice HTML content after cleaning
                    #print(f"Choice HTML after cleaning (first 50 chars): {text[:50]}...")

                """ if letter in answer_string:
                    text += " (correct answer)" """

                # Add choice to the choices list
                choices_list.append({letter: text})

            filtered_record["exam"]["choices"] = choices_list

        # print(filtered_record)
        # return the structured JSON
        del soup, soup2, record
        gc.collect()
        return filtered_record

    def orderDataJson(self):
        # Check if the file exists and read the existing data
        if os.path.exists(self.data_json_path):
            with open(self.data_json_path, "r") as json_file:
                try:
                    # Load existing data
                    existing_data = json.load(json_file)
                    # Ensure existing_data is a list
                    if not isinstance(existing_data, list):
                        raise ValueError(
                            "JSON file must contain an array at the root level."
                        )
                except (json.JSONDecodeError, ValueError):
                    # If the file is empty, not valid JSON, or not a list, initialize as an empty list
                    existing_data = []
        else:
            # If the file does not exist, initialize existing_data as an empty list
            existing_data = []

        # Ensure all timestamps are floats
        for entry in existing_data:
            # Check if 'timestamp' exists in the entry
            if "timestamp" in entry:
                # Convert timestamp to float if it's a string and non-empty
                if isinstance(entry["timestamp"], str):
                    if entry["timestamp"].strip() == "":  # Check for empty string
                        print(
                            f"Warning: Invalid timestamp found in entry: {entry}. Setting to None."
                        )
                        entry["timestamp"] = (
                            None  # or you can choose to remove the entry
                        )
                    else:
                        try:
                            entry["timestamp"] = float(entry["timestamp"])
                        except ValueError:
                            print(
                                f"Warning: Could not convert timestamp '{entry['timestamp']}' to float."
                            )
                            entry["timestamp"] = None  # Handle invalid string

        # Filter out entries with None timestamps
        existing_data = [
            entry for entry in existing_data if entry.get("timestamp") is not None
        ]

        # Sort the data by timestamp
        existing_data.sort(key=lambda x: x["timestamp"])

        # Write the updated data back to the JSON file
        with open(self.data_json_path, "w") as json_file:
            json.dump(existing_data, json_file, indent=4)

        print(f"Sorted json data on {self.data_json_path}")

    def archive_old_json(self, file_path=None):
        """Archives the old json_file file by renaming it to data_[modified_YYYY-MM-DD].json."""
        if file_path is None:
            file_path = self.data_json_path
        if os.path.exists(file_path):
            # Get the last modified time of the file
            modified_time = os.path.getmtime(file_path)

            timestamp_str = str(int(modified_time))

            # Convert the modified time to a datetime object
            # modified_date = datetime.fromtimestamp(modified_time)
            # Format the date as YYYY-MM-DD
            # formatted_date = modified_date.strftime("%Y-%m-%d")

            # Create a new filename for archiving
            archived_file_path = os.path.join(
                os.path.dirname(file_path), f"data_{timestamp_str}.json"
            )

            # Rename the existing data.json to data_[modified_YYYY-MM-DD].json
            os.rename(file_path, archived_file_path)
            print(f"Archived old data to {archived_file_path}")
        else:
            print("No existing data file to archive.")

    def get_discussion_categories(
        self, url="https://www.examtopics.com/discussions/", max_retries=5, delay=5
    ):
        # List to hold categories and links
        categories = []

        for attempt in range(max_retries):
            try:
                # Send a request to fetch the page content
                response = requests.get(
                    self.get_full_url(url), headers=self.headers)

                # Check if the request was successful
                if response.status_code == 200:
                    # Parse the page content
                    soup = BeautifulSoup(response.text, "lxml")

                    # Find all discussion rows
                    discussion_rows = soup.find_all(
                        "div", class_="row discussion-row")

                    # Loop through each discussion row to extract names and links
                    for row in discussion_rows:
                        # Find the title container
                        title_container = row.find(
                            "div", class_="dicussion-title-container"
                        )

                        # Extract the discussion category name
                        category_name = title_container.find("h2").text.strip()

                        # Extract the link for the discussion category
                        category_link = title_container.find(
                            "a", class_="discussion-link"
                        )["href"]

                        # Append the name and link as a tuple to the list
                        categories.append((category_name, category_link))

                    return categories  # Return categories if successful

                else:
                    print(
                        f"Failed to retrieve the page. Status code: {response.status_code}"
                    )

            except requests.RequestException as e:
                print(f"An error occurred: {e}")
                if attempt < max_retries - 1:
                    print(f"Retrying in {delay} seconds...")
                    time.sleep(delay)  # Wait before retrying

        return categories  # Return empty list if all retries fail

    def get_discussion_links_and_codes(self, url, max_retries=5, delay=5):
        # List to hold the discussion links and exam codes
        discussions = []

        for attempt in range(max_retries):
            try:
                print(self.get_full_url(url))
                # Send a request to fetch the page content
                response = requests.get(
                    self.get_full_url(url), headers=self.headers)

                # Check if the request was successful
                if response.status_code == 200:
                    # Parse the page content
                    soup = BeautifulSoup(response.text, "lxml")

                    # Find all discussion rows
                    discussion_rows = soup.find_all(
                        "div", class_="row discussion-row")
                    # Regex pattern to extract exam code
                    pattern = r"exam-(\w{2}-\d+)"

                    # Loop through each discussion row
                    for row in discussion_rows:
                        # Find the discussion link
                        link_tag = row.find("a", class_="discussion-link")
                        if link_tag:
                            # Extract the link
                            discussion_link = link_tag["href"]
                            # Use regex to find the exam code in the link
                            match = re.search(pattern, discussion_link)
                            if match:
                                # Extract the exam code
                                exam_code = match.group(1)
                                discussions.append(
                                    (discussion_link, exam_code)
                                )  # Append to the list

                    return discussions  # Return discussions if successful

                else:
                    print(
                        f"Failed to retrieve the page. Status code: {response.status_code}"
                    )

            except requests.RequestException as e:
                print(f"An error occurred: {e}")
                if attempt < max_retries - 1:
                    print(f"Retrying in {delay} seconds...")
                    time.sleep(delay)  # Wait before retrying

        return discussions  # Return empty list if all retries fail

    def get_provider_names_and_slugs(self, url="exams"):

        response = self.fetch_url_old(self.get_full_url(url))
        # List to hold provider names and their slug names
        providers = []

        # Parse the HTML content
        soup = BeautifulSoup(response.text, "lxml")

        # Find all provider list links
        provider_links = soup.find_all("div", class_="provider-list-link")

        for link in provider_links:
            # Extract the anchor tag
            anchor = link.find("a")

            if anchor:
                # Get the provider name, excluding the <span>
                provider_name = anchor.contents[
                    0
                ].strip()  # Use contents to get only the text
                # Get the slug name from the href attribute
                slug_name = (
                    anchor["href"].strip("/").split("/")[-1]
                )  # Extract slug from href

                # Append the name and slug to the list
                providers.append((provider_name, slug_name))

        """ for provider in providers:
            yield provider """
        return providers

    async def fetch_url(self, url, timeout=30, retries=20, base_sleep=1):
        """
        Asynchronously fetches the content from the specified URL with retry logic.
        """

        for attempt in range(retries):
            try:
                """async with self.session.get(
                    url,
                    headers={"User-Agent": self.get_random_user_agent()},
                    timeout=timeout,
                ) as response:"""
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=timeout) as response:
                        response.raise_for_status()  # Raise an error for bad responses (4xx or 5xx)
                        return (
                            await response.text()
                        )  # Return the content of the response
            except aiohttp.ClientError as e:
                # Handle aiohttp-specific errors (e.g., 4xx, 5xx, connection errors)
                print(f"Attempt {attempt + 1} failed: {e}")
                if hasattr(e, "response") and e.response.status_code == 404:
                    print("404 Not Found error, no need to retry.")
                    return None
            except asyncio.TimeoutError as e:
                # Handle timeout errors
                print(f"Attempt {attempt + 1} failed: TimeoutError")
            except Exception as e:
                # Handle any other exceptions
                print(f"Attempt {attempt + 1} failed: {e}")

            # Exponential backoff sleep
            if attempt < retries - 1:
                sleep_time = base_sleep * (1.5 ** min(attempt, 5))
                print(f"Sleeping for {sleep_time}s before retrying...")
                await asyncio.sleep(sleep_time)

    def fetch_url_old(self, url, timeout=5, retries=20, base_sleep=1):
        """
        Fetches the content from the specified URL with retry logic.

        Args:
            url (str): The URL to fetch.
            headers (dict, optional): Optional headers to include in the request.
            timeout (int, optional): The timeout for the request in seconds. Default is 10.

        Returns:
            str: The content of the response if successful, None if an error occurs.
        """
        # Create a session and mount it for retries
        session = requests.Session()
        retry = Retry(total=3, backoff_factor=10,
                      status_forcelist=[500, 502, 503, 504])
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        for attempt in range(retries):
            try:
                response = session.get(
                    url,
                    headers={"User-Agent": self.get_random_user_agent()},
                    timeout=timeout,
                )  # Adjust timeout as necessary
                response.raise_for_status()  # Raise an error for bad responses (4xx or 5xx)
                session.close()
                return response  # Return the content of the response
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if e.response is not None and e.response.status_code == 404:
                    print("404 Not Found error, no need to retry.")
                    return None
                # Exponential backoff sleep
                sleep_time = base_sleep * (
                    2 ** min(attempt, 5)
                )  # Cap the increase after 5 attempts
                print(f"Start sleeping for the next {sleep_time}s")
                time.sleep(sleep_time)  # Wait before retrying

    def get_exam_info(self, provider_slug):
        # List to hold exam information
        exams = []

        # Send a request to fetch the page content
        response = self.fetch_url_old(
            self.get_full_url(f"exams/{provider_slug}/"))

        # Parse the HTML content
        soup = BeautifulSoup(response.text, "lxml")

        # Find all list items in the exam list
        exam_items = soup.find_all("li")

        for item in exam_items:
            # Extract the anchor tag
            anchor = item.find("a", class_="popular-exam-link")

            if anchor:
                # Get the second last segment
                exam_code = anchor["href"].split("/")[-2]
                # Get the exam name from the anchor text
                exam_name = anchor.text.replace(exam_code + ":", "").strip()
                # Check for the popularity badge
                is_popular = bool(
                    item.find(
                        "span", class_="badge badge-primary popular-badge")
                )

                # Append the information as a dictionary to the list
                exams.append(
                    {
                        "exam_code": exam_code,
                        "exam_name": exam_name,
                        "is_popular": is_popular,
                    }
                )
        """ for exam in exams:
            yield exam """
        return exams

    async def get_exam_info_new(self, provider_slug):
        # List to hold exam information
        exams = []

        # Send a request to fetch the page content
        html_content = await self.fetch_url(
            self.get_full_url(f"exams/{provider_slug}/")
        )
        if not html_content:
            return None  # Return None if the request failed

        # Parse the HTML content
        soup = BeautifulSoup(html_content, "lxml")

        # Find all list items in the exam list
        exam_items = soup.find_all("li")

        for item in exam_items:
            # Extract the anchor tag
            anchor = item.find("a", class_="popular-exam-link")

            if anchor:
                # Get the second last segment
                exam_code = anchor["href"].split("/")[-2]
                # Get the exam name from the anchor text
                exam_name = anchor.text.replace(exam_code + ":", "").strip()
                # Check for the popularity badge
                is_popular = bool(
                    item.find(
                        "span", class_="badge badge-primary popular-badge")
                )

                # Append the information as a dictionary to the list
                exams.append(
                    {
                        "exam_code": exam_code,
                        "exam_name": exam_name,
                        "is_popular": is_popular,
                    }
                )
        """ for exam in exams:
            yield exam """
        return exams

    def get_exam_details(self, provider_slug, exam_code):

        response = self.fetch_url_old(
            self.get_full_url(f"exams/{provider_slug}/{exam_code}/view/1/")
        )

        # Parse the page content
        soup = BeautifulSoup(response.text, "lxml")

        # Extract the last updated date
        date = soup.find("span", class_="examQa__date")

        # Check if the element was found
        if date is not None:
            date_text = date.text
        else:
            return None

        last_updated_date_string = date_text.split("Last updated on ")[
            -1
        ].strip()  # Extract date part

        # Use the custom parseDate function to get the formatted date
        formatted_date = self.parseDate(last_updated_date_string)

        # Extract the total number of questions
        # questions_text = soup.find('div', class_='examQa__item').find_all('span')[1].text
        total_questions = int(
            soup.find_all("div", class_="examQa__item")[
                3].find_all("span")[1].text
        )  # Get the number before "questions"
        print(formatted_date, total_questions)
        return formatted_date, total_questions

    async def get_exam_details_new(self, provider_slug, exam_code):

        html_content = await self.fetch_url(
            self.get_full_url(f"exams/{provider_slug}/{exam_code}/view/1/")
        )
        if not html_content:
            return None  # Return None if the request failed

        # Parse the HTML content
        soup = BeautifulSoup(html_content, "lxml")

        # Extract the last updated date
        date = soup.find("span", class_="examQa__date")

        # Check if the element was found
        if date is not None:
            date_text = date.text
        else:
            return None

        last_updated_date_string = date_text.split("Last updated on ")[
            -1
        ].strip()  # Extract date part

        # Use the custom parseDate function to get the formatted date
        formatted_date = self.parseDate(last_updated_date_string)

        # Extract exam details from the new HTML structure
        try:
            # Extract exam name from the h2 element in examQa container
            examqa_container = soup.find("div", class_="examQa")
            if examqa_container:
                h2_element = examqa_container.find("h2")
                if h2_element:
                    exam_name = h2_element.text.strip()
                else:
                    exam_name = f"Unknown Exam {exam_code}"
            else:
                exam_name = f"Unknown Exam {exam_code}"

            # Extract vendor name from provider_slug (capitalize first letter)
            vendor_name = provider_slug.capitalize()

            # Extract total questions from examQa__footer-info-item elements
            total_questions = 0
            footer_info_items = soup.find_all("div", class_="examQa__footer-info-item")
            for item in footer_info_items:
                item_text = item.text.strip()
                # Look for text containing "Questions & Answers Included"
                if "Questions & Answers Included" in item_text:
                    # Extract the number from the text
                    import re
                    numbers = re.findall(r'\d+', item_text)
                    if numbers:
                        total_questions = int(numbers[0])
                    break

            # If we couldn't find the questions count, try alternative methods
            if total_questions == 0:
                # Look for any span containing just a number followed by questions text
                all_spans = soup.find_all("span")
                for span in all_spans:
                    span_text = span.text.strip()
                    if span_text.isdigit():
                        # Check if the next sibling or nearby text mentions questions
                        parent = span.parent
                        if parent and ("question" in parent.text.lower() or "answer" in parent.text.lower()):
                            total_questions = int(span_text)
                            break

            # Fallback: set to 0 if we still couldn't find it
            if total_questions == 0:
                print(f"Warning: Could not extract total questions count for {exam_code}")
                total_questions = 0

        except Exception as e:
            print(f"Error extracting exam details: {e}")
            # Fallback values
            vendor_name = provider_slug.capitalize()
            exam_name = f"Unknown Exam {exam_code}"
            total_questions = 0
        # print(formatted_date, total_questions)
        return {
            "code": exam_code,
            "name": exam_name,
            "vendor_name": vendor_name,
            "vendor_code": provider_slug,
            "date_updated": formatted_date,
            "claimed_total": total_questions,
            "timestamp": datetime.now(tz),
        }

    async def get_exam_details_wrapper(self, name_info):
        """
        Wrapper function to call function2 with name and info.
        """
        provider_slug, exam_code = name_info  # Unpack the tuple
        return await self.get_exam_details_new(provider_slug, exam_code)

    async def session_close(self):
        """Close the aiohttp session if it exists."""
        if hasattr(self, 'session') and self.session is not None:
            await self.session.close()
            print("Session closed successfully.")
