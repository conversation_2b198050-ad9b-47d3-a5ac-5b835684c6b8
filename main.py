import sys
import os

sys.path.append(os.path.abspath("./soup/"))
sys.path.append(os.path.abspath("./jinja2/"))

#from bot import DemoBot
from bot_soup import SoupBot
from bot_jinja import JinjaBot
from urllib.parse import urljoin
import json
import time

examdumps_folder = "examdumps"

# Get user input for the URL and exam code
url = input("Enter the discussion URL: ")
if not url:
    url = "https://www.examtopics.com/discussions/pmi/"
    # https://www.examtopics.com/discussions/amazon/
    # https://www.examtopics.com/discussions/scrum/
    # https://www.examtopics.com/discussions/hashicorp/
    # https://www.examtopics.com/discussions/cncf/
    # https://www.examtopics.com/discussions/microsoft/
    # https://www.examtopics.com/discussions/pmi/
    print(f"You entered an empty value. Using the default value: {url}")
else:
    # If the input is not empty, use the user's input
    print(f"You entered: {url}")

exam_code = input("Enter the exam code: ")
if not exam_code:
    exam_code = "pmp"
    print(f"You entered an empty value. Using the default value: {exam_code}")
else:
    # If the input is not empty, use the user's input
    print(f"You entered: {exam_code}")

#bot = DemoBot()
bot_soup = SoupBot(exam_code, examdumps_folder)
bot_jinja = JinjaBot(exam_code, examdumps_folder)

x = 1
# x=100

# Get the total number of pages
LAST_PAGE = bot_soup.get_total_pages(urljoin(url, str(x)))
print(f"Total number of pages: {LAST_PAGE}")

#LAST_PAGE = 100

count = 0
list = []

bot_soup.archive_old_json()

# Looping through pagination
while x < LAST_PAGE + 1:
    print("Page ", x)
    records = bot_soup.find_all_records(urljoin(url, str(x))) or []

    for record in records:
        bot_soup.processPage(record)

    count += len(records)
    list.extend(records)
    if len(records) > 1:
        print(f"{count} items so far.")

    x += 1
    
bot_soup.orderDataJson()

bot_jinja.create_all()

time.sleep(0.5)

bot_jinja.remove_dates_from_files()

print(f"Total count: {count}")