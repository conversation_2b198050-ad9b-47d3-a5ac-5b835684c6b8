"""
AWS-specific PDF processing functionality for exam subject extraction.

This module handles downloading and processing AWS certification exam guide PDFs
to extract domain and task information.
"""

import os
import requests
import pdfplumber
import sys
from datetime import datetime
from typing import List, Optional, Tuple

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
mongo_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, mongo_dir)

from utils.gpt_client import send_to_gpt


def download_pdf(url: str, output_file: str = "downloaded.pdf") -> bool:
    """
    Download a PDF file from the given URL.
    
    Args:
        url: URL of the PDF to download
        output_file: Local filename to save the PDF
        
    Returns:
        True if download successful, False otherwise
    """
    try:
        print("Downloading PDF...")
        response = requests.get(url)
        response.raise_for_status()
        
        with open(output_file, "wb") as f:
            f.write(response.content)
        
        print(f"PDF downloaded successfully and saved as {output_file}")
        return True
        
    except Exception as e:
        print(f"Error downloading PDF: {e}")
        return False


def extract_text(pdf_file: str = "downloaded.pdf") -> Optional[str]:
    """
    Extract text content from a PDF file.
    
    Args:
        pdf_file: Path to the PDF file
        
    Returns:
        Extracted text content or None if extraction fails
    """
    try:
        print("Extracting text from PDF...")
        all_text = ""
        
        with pdfplumber.open(pdf_file) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    all_text += text + " "
        
        if not all_text.strip():
            print("Warning: No text was extracted from the PDF.")
            return None
            
        return all_text.strip()
        
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None


def clean_text(text: str) -> Optional[str]:
    """
    Clean the extracted text by removing irrelevant sections and processing with AI.
    
    This function:
    1. Extracts content between "Domain 1" and "Appendix"
    2. Sends the content to GPT for structured summarization
    3. Returns the cleaned and summarized text
    
    Args:
        text: Raw extracted text from PDF
        
    Returns:
        Cleaned and AI-processed text or None if processing fails
    """
    try:
        start_keyword = "Domain 1"
        end_keyword = "Appendix"

        # Find the first occurrence of "Domain 1"
        start_index = text.find(start_keyword)
        if start_index == -1:
            raise ValueError(f"Start keyword '{start_keyword}' not found in the text.")

        # Find the last occurrence of "Appendix"
        end_index = text.rfind(end_keyword)
        if end_index == -1:
            raise ValueError(f"End keyword '{end_keyword}' not found in the text.")

        # Extract the relevant portion of the text
        cleaned_text = text[start_index:end_index + len(end_keyword)]
        cleaned_text = cleaned_text.replace('\n', ' ').replace('"', r'\"').strip()

        # Save the cleaned text for debugging
        debug_file = "cleaned_text_debug.txt"
        with open(debug_file, "w", encoding="utf-8") as f:
            f.write(cleaned_text)
        print(f"Cleaned text saved to '{debug_file}' for debugging.")

        # Process with GPT for structured summary
        ai_summary = send_to_gpt(
            cleaned_text,
            "identify the specific AWS certification exam code from this text. Then provide a structured summary of each domain and its task statements using the exact numbering format (Domain 1, Task 1.1, etc.). Include all key technical requirements, knowledge areas, and skills mentioned. Be comprehensive but concise."
        )
        
        if not ai_summary:
            print("Warning: No AI summary received, using original cleaned text")
            return cleaned_text
        
        # Clean the AI summary
        final_text = ai_summary.replace('\n', ' ').replace('"', r'\"').strip()

        # Save the AI-processed text for debugging
        ai_debug_file = "cleaned_text_debug_after_ai_summary.txt"
        with open(ai_debug_file, "w", encoding="utf-8") as f:
            f.write(final_text)
        print(f"AI-processed text saved to '{ai_debug_file}' for debugging.")

        return final_text

    except Exception as e:
        print(f"Error cleaning text: {e}")
        return text  # Return original text as fallback


def extract_exam_code_and_domains(gpt_response: str) -> Tuple[Optional[str], List[str]]:
    """
    Extract exam code and domains from GPT response.
    
    Expected format: "[exam code]|domain 1|domain 2|..."
    
    Args:
        gpt_response: Response from GPT API
        
    Returns:
        Tuple of (exam_code, domains_list) or (None, []) if extraction fails
    """
    try:
        # Split and clean parts
        parts = [part.strip() for part in gpt_response.split("|")]

        # Validate format
        if len(parts) < 2:
            print(f"Error: Invalid format in GPT response. Expected 'code|domain1|domain2', got: '{gpt_response}'")
            return None, []

        exam_code = parts[0]
        domains = parts[1:]

        # Validate exam code is not empty
        if not exam_code:
            print("Error: Empty exam code in GPT response")
            return None, []

        # Validate at least one domain exists
        if not domains:
            print("Error: No domains found in GPT response")
            return None, []

        return exam_code, domains

    except Exception as e:
        print(f"Error extracting exam code and domains: {e}")
        return None, []


def process_aws_pdf(pdf_url: str) -> Tuple[Optional[str], List[str]]:
    """
    Complete AWS PDF processing pipeline.
    
    Args:
        pdf_url: URL of the AWS exam guide PDF
        
    Returns:
        Tuple of (exam_code, domains_list) or (None, []) if processing fails
    """
    try:
        # Download PDF
        if not download_pdf(pdf_url):
            return None, []
        
        # Extract text
        extracted_text = extract_text("downloaded.pdf")
        if not extracted_text:
            print("Error: No text extracted from PDF")
            return None, []
        
        # Clean and process text
        cleaned_text = clean_text(extracted_text)
        if not cleaned_text:
            print("Error: Cleaned text is empty")
            return None, []
        
        # Get structured response from GPT
        gpt_response = send_to_gpt(
            cleaned_text, 
            "Please state exam code, exam name and the domains in the following format: [exam code]|[domain name 1]|[domain name 2]| etc"
        )
        
        if not gpt_response:
            print("Error: No GPT response received")
            return None, []
        
        print(f"GPT Response: {gpt_response}")
        
        # Extract exam code and domains
        exam_code, domains = extract_exam_code_and_domains(gpt_response)
        
        if exam_code and domains:
            print(f"Successfully processed: {exam_code}")
            print(f"Domains: {domains}")
            return exam_code, domains
        else:
            print("Error: Failed to extract exam code and domains")
            return None, []
            
    except Exception as e:
        print(f"Error in AWS PDF processing pipeline: {e}")
        return None, []


# AWS exam code to PDF URL mapping
AWS_EXAM_PDF_MAPPING = {
    # Foundational
    "aws-certified-cloud-practitioner-clf-c02": "https://d1.awsstatic.com/training-and-certification/docs-cloud-practitioner/AWS-Certified-Cloud-Practitioner_Exam-Guide.pdf",

    # Associate Level
    "aws-certified-solutions-architect-associate-saa-c03": "https://d1.awsstatic.com/training-and-certification/docs-sa-assoc/AWS-Certified-Solutions-Architect-Associate_Exam-Guide.pdf",
    "aws-certified-developer-associate-dva-c02": "https://d1.awsstatic.com/training-and-certification/docs-dev-associate/AWS-Certified-Developer-Associate_Exam-Guide.pdf",
    "aws-certified-sysops-administrator-associate": "https://d1.awsstatic.com/training-and-certification/docs-sysops-associate/AWS-Certified-SysOps-Administrator-Associate_Exam-Guide.pdf",
    "aws-certified-data-engineer-associate-dea-c01": "https://d1.awsstatic.com/training-and-certification/docs-data-engineer-associate/AWS-Certified-Data-Engineer-Associate_Exam-Guide.pdf",
    "aws-certified-machine-learning-engineer-associate-mla-c01": "https://d1.awsstatic.com/training-and-certification/docs-machine-learning-engineer-associate/AWS-Certified-Machine-Learning-Engineer-Associate_Exam-Guide.pdf",

    # Professional Level
    "aws-certified-solutions-architect-professional-sap-c02": "https://d1.awsstatic.com/training-and-certification/docs-sa-pro/AWS-Certified-Solutions-Architect-Professional_Exam-Guide.pdf",
    "aws-certified-devops-engineer-professional-dop-c02": "https://d1.awsstatic.com/training-and-certification/docs-devops-pro/AWS-Certified-DevOps-Engineer-Professional_Exam-Guide.pdf",

    # Specialty
    "aws-certified-advanced-networking-specialty-ans-c01": "https://d1.awsstatic.com/training-and-certification/docs-advnetworking-spec/AWS-Certified-Advanced-Networking-Specialty_Exam-Guide.pdf",
    "aws-certified-security-specialty-scs-c02": "https://d1.awsstatic.com/training-and-certification/docs-security-spec/AWS-Certified-Security-Specialty_Exam-Guide.pdf",
    "aws-certified-machine-learning-specialty": "https://d1.awsstatic.com/training-and-certification/docs-ml/AWS-Certified-Machine-Learning-Specialty_Exam-Guide.pdf",
    "aws-certified-ai-practitioner-aif-c01": "https://d1.awsstatic.com/training-and-certification/docs-ai-practitioner/AWS-Certified-AI-Practitioner_Exam-Guide.pdf",
}

# Legacy list for backward compatibility
AWS_EXAM_PDF_URLS = list(AWS_EXAM_PDF_MAPPING.values())


def get_pdf_url_for_exam_code(exam_code: str) -> Optional[str]:
    """
    Get the PDF URL for a specific AWS exam code.

    Args:
        exam_code: AWS exam code

    Returns:
        PDF URL if found, None otherwise
    """
    # Direct mapping lookup
    pdf_url = AWS_EXAM_PDF_MAPPING.get(exam_code)
    if pdf_url:
        return pdf_url

    # Fallback: try case-insensitive lookup
    exam_code_lower = exam_code.lower()
    for code, url in AWS_EXAM_PDF_MAPPING.items():
        if code.lower() == exam_code_lower:
            return url

    print(f"⚠️ Warning: No PDF URL found for exam code: {exam_code}")
    return None
