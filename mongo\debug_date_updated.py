#!/usr/bin/env python3
"""
Debug script for date_updated field handling in MongoDB update process.

This script tests the specific exam code: aws-certified-solutions-architect-professional-sap-c02
Expected URL: https://www.examtopics.com/exams/amazon/aws-certified-solutions-architect-professional-sap-c02/view/1/
Expected last_updated_date_string: "Aug. 11, 2025"
"""

import asyncio
import platform
import sys
import os
from datetime import datetime
import pytz

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bot_soup2 import SoupBot2
from pymongo import MongoClient

# Test configuration
TEST_EXAM_CODE = "aws-certified-solutions-architect-professional-sap-c02"
TEST_PROVIDER_SLUG = "amazon"
EXPECTED_URL = f"https://www.examtopics.com/exams/{TEST_PROVIDER_SLUG}/{TEST_EXAM_CODE}/view/1/"
EXPECTED_DATE_STRING = "Aug. 11, 2025"

# Initialize timezone
tz = pytz.timezone("Asia/Hong_Kong")

# Initialize MongoDB client
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0&compressors=zstd",
    compressors="zstd",
)
exam_db = client["exam"]
exam_detail_collection = exam_db["detail"]

# Initialize SoupBot2
bot_soup = SoupBot2(
    image_download_path="C:\\Users\\<USER>\\dev\\rn_node\\src\\public\\images",
    qna_images_folder=""
)

async def test_html_content():
    """Test fetching and parsing the HTML content directly"""
    print(f"🔍 Testing HTML content fetch for {TEST_EXAM_CODE}")
    print(f"📍 URL: {EXPECTED_URL}")
    print("-" * 80)

    try:
        # Fetch the HTML content
        url = f"exams/{TEST_PROVIDER_SLUG}/{TEST_EXAM_CODE}/view/1/"
        html_content = await bot_soup.fetch_url(bot_soup.get_full_url(url))

        if not html_content:
            print("❌ Failed to fetch HTML content")
            return None

        print(f"✅ HTML content fetched, length: {len(html_content)} characters")

        # Parse with BeautifulSoup
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, "lxml")

        # Check for the date element
        print("\n📅 Looking for date element...")
        date_element = soup.find("span", class_="examQa__date")
        if date_element:
            date_text = date_element.text
            print(f"   ✅ Found date element: '{date_text}'")

            # Extract the date part
            if "Last updated on " in date_text:
                last_updated_date_string = date_text.split("Last updated on ")[-1].strip()
                print(f"   📅 Extracted date string: '{last_updated_date_string}'")

                # Test the parseDate function
                formatted_date = bot_soup.parseDate(last_updated_date_string)
                print(f"   📅 Parsed date: {formatted_date}")
            else:
                print(f"   ⚠️  Date text doesn't contain 'Last updated on': '{date_text}'")
        else:
            print("   ❌ Date element not found")

        # Check for examQa__item elements
        print("\n🔍 Looking for examQa__item elements...")
        exam_items = soup.find_all("div", class_="examQa__item")
        print(f"   Found {len(exam_items)} examQa__item elements")

        for i, item in enumerate(exam_items):
            spans = item.find_all("span")
            print(f"   Item {i}: {len(spans)} spans")
            for j, span in enumerate(spans):
                print(f"     Span {j}: '{span.text.strip()}'")

        # If no examQa__item elements found, let's look for alternative structures
        if len(exam_items) == 0:
            print("\n🔍 No examQa__item found, looking for alternative structures...")

            # Look for any divs with "exam" in the class name
            exam_divs = soup.find_all("div", class_=lambda x: x and "exam" in x.lower())
            print(f"   Found {len(exam_divs)} divs with 'exam' in class name")
            for i, div in enumerate(exam_divs[:5]):  # Show first 5
                print(f"     Div {i}: class='{div.get('class')}', text preview: '{div.text[:100]}...'")

            # Look for any elements with "item" in the class name
            item_elements = soup.find_all(class_=lambda x: x and "item" in x.lower())
            print(f"   Found {len(item_elements)} elements with 'item' in class name")
            for i, elem in enumerate(item_elements[:5]):  # Show first 5
                print(f"     Element {i}: tag='{elem.name}', class='{elem.get('class')}', text preview: '{elem.text[:50]}...'")

            # Look for the main examQa container
            examqa_container = soup.find("div", class_="examQa")
            if examqa_container:
                print(f"\n✅ Found examQa container")
                print(f"   Container text preview: '{examqa_container.text[:200]}...'")

                # Look for all child elements
                children = examqa_container.find_all(recursive=False)
                print(f"   Direct children: {len(children)}")
                for i, child in enumerate(children):
                    print(f"     Child {i}: tag='{child.name}', class='{child.get('class')}', text='{child.text[:50]}...'")

                # Look for any spans within the container
                spans = examqa_container.find_all("span")
                print(f"   All spans in container: {len(spans)}")
                for i, span in enumerate(spans):
                    print(f"     Span {i}: text='{span.text.strip()}'")
            else:
                print("\n❌ No examQa container found")

            # Save a portion of the HTML for manual inspection
            print("\n💾 Saving HTML snippet for manual inspection...")
            with open("debug_html_snippet.html", "w", encoding="utf-8") as f:
                # Find the area around where exam details should be
                body = soup.find("body")
                if body:
                    f.write(str(body)[:10000])  # First 10k characters of body
                else:
                    f.write(str(soup)[:10000])  # First 10k characters of entire HTML
            print("   Saved to debug_html_snippet.html")

        return html_content, soup

    except Exception as e:
        print(f"❌ Error in HTML content test: {e}")
        import traceback
        traceback.print_exc()
        return None, None

async def test_get_exam_details_new():
    """Test the get_exam_details_new function directly"""
    print(f"\n🔍 Testing get_exam_details_new for {TEST_EXAM_CODE}")
    print(f"📍 Expected URL: {EXPECTED_URL}")
    print(f"📅 Expected date string: {EXPECTED_DATE_STRING}")
    print("-" * 80)

    try:
        # First test HTML content
        html_content, soup = await test_html_content()
        if not html_content:
            print("❌ Cannot proceed without HTML content")
            return None

        # Now call the function directly
        result = await bot_soup.get_exam_details_new(TEST_PROVIDER_SLUG, TEST_EXAM_CODE)

        if result is None:
            print("❌ get_exam_details_new returned None")
            return None

        print("✅ get_exam_details_new returned data:")
        for key, value in result.items():
            print(f"   {key}: {value}")

        # Check if date_updated field exists and has the expected value
        if 'date_updated' in result:
            date_updated = result['date_updated']
            print(f"\n📅 Date Updated Analysis:")
            print(f"   Raw date_updated: {date_updated}")
            print(f"   Type: {type(date_updated)}")

            if date_updated == "2025-08-11":  # Expected parsed format
                print("   ✅ Date matches expected parsed format (2025-08-11)")
            else:
                print(f"   ⚠️  Date does not match expected format. Expected: 2025-08-11, Got: {date_updated}")
        else:
            print("❌ date_updated field is missing from result")

        return result

    except Exception as e:
        print(f"❌ Error in get_exam_details_new: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_get_exam_details_wrapper():
    """Test the get_exam_details_wrapper function"""
    print(f"\n🔍 Testing get_exam_details_wrapper for {TEST_EXAM_CODE}")
    print("-" * 80)
    
    try:
        # Call the wrapper function
        name_info = (TEST_PROVIDER_SLUG, TEST_EXAM_CODE)
        result = await bot_soup.get_exam_details_wrapper(name_info)
        
        if result is None:
            print("❌ get_exam_details_wrapper returned None")
            return None
        
        print("✅ get_exam_details_wrapper returned data:")
        for key, value in result.items():
            print(f"   {key}: {value}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error in get_exam_details_wrapper: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_process_multiple_pages():
    """Test the process_multiple_pages function with our exam code"""
    print(f"\n🔍 Testing process_multiple_pages for {TEST_EXAM_CODE}")
    print("-" * 80)
    
    try:
        # Prepare the data as it would be in update_exam_details
        name_info_pairs = [(TEST_PROVIDER_SLUG, TEST_EXAM_CODE)]
        
        # Call process_multiple_pages
        scrapped_exam_details = await bot_soup.process_multiple_pages(
            name_info_pairs,
            bot_soup.get_exam_details_wrapper,
        )
        
        if not scrapped_exam_details:
            print("❌ process_multiple_pages returned empty list")
            return None
        
        print(f"✅ process_multiple_pages returned {len(scrapped_exam_details)} items:")
        for i, exam_detail in enumerate(scrapped_exam_details):
            print(f"   Item {i}:")
            if exam_detail is None:
                print("     None")
            else:
                for key, value in exam_detail.items():
                    print(f"     {key}: {value}")
        
        return scrapped_exam_details
        
    except Exception as e:
        print(f"❌ Error in process_multiple_pages: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_mongodb_query():
    """Test querying the current state in MongoDB"""
    print(f"\n🔍 Testing MongoDB query for {TEST_EXAM_CODE}")
    print("-" * 80)
    
    try:
        # Query the current document
        current_doc = exam_detail_collection.find_one({"code": TEST_EXAM_CODE})
        
        if current_doc is None:
            print("❌ No document found in MongoDB for this exam code")
            return None
        
        print("✅ Current MongoDB document:")
        for key, value in current_doc.items():
            print(f"   {key}: {value}")
        
        # Check date_updated field specifically
        if 'date_updated' in current_doc:
            date_updated = current_doc['date_updated']
            print(f"\n📅 Current date_updated in MongoDB:")
            print(f"   Value: {date_updated}")
            print(f"   Type: {type(date_updated)}")
        else:
            print("❌ date_updated field is missing from MongoDB document")
        
        return current_doc
        
    except Exception as e:
        print(f"❌ Error querying MongoDB: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_full_pipeline():
    """Test the full pipeline from scraping to MongoDB update"""
    print(f"\n🔍 Testing full pipeline for {TEST_EXAM_CODE}")
    print("=" * 80)
    
    # Step 1: Test direct scraping
    scraped_data = await test_get_exam_details_new()
    if scraped_data is None:
        print("❌ Pipeline failed at scraping step")
        return
    
    # Step 2: Test wrapper function
    wrapper_data = await test_get_exam_details_wrapper()
    if wrapper_data is None:
        print("❌ Pipeline failed at wrapper step")
        return
    
    # Step 3: Test process_multiple_pages
    processed_data = await test_process_multiple_pages()
    if not processed_data or processed_data[0] is None:
        print("❌ Pipeline failed at process_multiple_pages step")
        return
    
    # Step 4: Test MongoDB query
    mongodb_data = test_mongodb_query()
    
    # Step 5: Compare data at each stage
    print(f"\n📊 Data Comparison Across Pipeline:")
    print("=" * 80)
    
    stages = [
        ("Scraped Data", scraped_data),
        ("Wrapper Data", wrapper_data),
        ("Processed Data", processed_data[0] if processed_data else None),
        ("MongoDB Data", mongodb_data)
    ]
    
    for stage_name, data in stages:
        print(f"\n{stage_name}:")
        if data is None:
            print("   None")
        else:
            date_updated = data.get('date_updated', 'MISSING')
            print(f"   date_updated: {date_updated} (type: {type(date_updated)})")

async def test_mongodb_update():
    """Test the actual MongoDB update process"""
    print(f"\n🔍 Testing MongoDB update for {TEST_EXAM_CODE}")
    print("=" * 80)

    try:
        # Import the update function
        from qna_updater import update_exam_details

        # Run the update for our specific exam code
        print(f"🔄 Running update_exam_details for {TEST_EXAM_CODE}...")
        result = await update_exam_details([TEST_EXAM_CODE])

        print(f"✅ Update completed. Result: {result}")

        # Check the updated document
        print(f"\n📊 Checking updated MongoDB document...")
        updated_doc = exam_detail_collection.find_one({"code": TEST_EXAM_CODE})

        if updated_doc:
            print("✅ Updated MongoDB document:")
            for key, value in updated_doc.items():
                print(f"   {key}: {value}")

            # Check if date_updated was updated correctly
            if 'date_updated' in updated_doc:
                date_updated = updated_doc['date_updated']
                print(f"\n📅 MongoDB date_updated after update:")
                print(f"   Value: {date_updated}")
                print(f"   Type: {type(date_updated)}")

                if date_updated == "2025-08-11":
                    print("   ✅ SUCCESS: date_updated correctly updated to 2025-08-11!")
                else:
                    print(f"   ❌ ISSUE: Expected 2025-08-11, got {date_updated}")
            else:
                print("   ❌ date_updated field missing from updated document")
        else:
            print("❌ No document found after update")

    except Exception as e:
        print(f"❌ Error in MongoDB update test: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main function to run all tests"""
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    print("🚀 Starting date_updated field debugging")
    print(f"🎯 Target exam code: {TEST_EXAM_CODE}")
    print(f"🌐 Expected URL: {EXPECTED_URL}")
    print(f"📅 Expected date string: {EXPECTED_DATE_STRING}")
    print("=" * 80)

    try:
        # First run the pipeline test to verify scraping works
        await test_full_pipeline()

        # Then test the actual MongoDB update
        await test_mongodb_update()

    except Exception as e:
        print(f"❌ Error in main: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await bot_soup.session_close()
        client.close()
        print("\n🏁 Debug session completed")

if __name__ == "__main__":
    asyncio.run(main())
